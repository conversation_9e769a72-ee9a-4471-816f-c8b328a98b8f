const { Pool } = require("pg");
const mysql = require("mysql2/promise");
const sqlite3 = require("sqlite3").verbose();
const { promisify } = require("util");
const EventEmitter = require("events");

class DatabaseConnection extends EventEmitter {
  constructor(type, config) {
    super();
    this.type = type;
    this.config = config;
    this.connection = null;
    this.isConnected = false;
    this.lastActivity = new Date();
    this.connectionId = `${type}_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    this.metadata = {
      createdAt: new Date(),
      queriesExecuted: 0,
      lastError: null,
      connectionTime: null,
    };
  }

  async connect() {
    try {
      switch (this.type) {
        case "postgresql":
          this.connection = new Pool({
            ...this.config,
            max: process.env.MAX_CONNECTIONS || 10,
            connectionTimeoutMillis: process.env.CONNECTION_TIMEOUT || 10000,
          });
          // Test the connection
          await this.connection.query("SELECT NOW()");
          break;

        case "mysql":
          this.connection = await mysql.createPool({
            ...this.config,
            connectionLimit: process.env.MAX_CONNECTIONS || 10,
            connectTimeout: process.env.CONNECTION_TIMEOUT || 10000,
          });
          // Test the connection
          await this.connection.query("SELECT 1");
          break;

        case "sqlite":
          this.connection = new sqlite3.Database(
            this.config.filename || ":memory:",
            sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
            (err) => {
              if (err) throw err;
            }
          );
          // Promisify SQLite methods
          this.connection.run = promisify(this.connection.run).bind(
            this.connection
          );
          this.connection.get = promisify(this.connection.get).bind(
            this.connection
          );
          this.connection.all = promisify(this.connection.all).bind(
            this.connection
          );
          // Test the connection
          await this.connection.get("SELECT 1");
          break;

        default:
          throw new Error(`Unsupported database type: ${this.type}`);
      }
      return { success: true };
    } catch (error) {
      console.error("Database connection error:", error);
      throw new Error(
        `Failed to connect to ${this.type} database: ${error.message}`
      );
    }
  }

  async query(sql, params = []) {
    if (!this.connection) {
      throw new Error("No active database connection");
    }

    try {
      let result;
      if (this.type === "sqlite") {
        // SQLite returns rows directly for SELECT, but we need to standardize the response
        const isSelect = sql.trim().toLowerCase().startsWith("select");
        if (isSelect) {
          result = await this.connection.all(sql, params);
          return { rows: result, rowCount: result.length };
        }
        result = await this.connection.run(sql, params);
        return { rowCount: result.changes || 0 };
      }

      // For PostgreSQL and MySQL
      const [rows, fields] = await this.connection.query(sql, params);
      return {
        rows,
        rowCount: Array.isArray(rows) ? rows.length : rows.affectedRows || 0,
        fields: fields?.map((f) => ({
          name: f.name || f.columnName,
          type: f.type || f.dataType,
          table: f.table,
        })),
      };
    } catch (error) {
      console.error("Query error:", error);
      throw new Error(`Query failed: ${error.message}`);
    }
  }

  async close() {
    if (!this.connection) return;

    try {
      if (this.type === "postgresql") {
        await this.connection.end();
      } else if (this.type === "mysql") {
        await this.connection.end();
      } else if (this.type === "sqlite") {
        await new Promise((resolve, reject) => {
          this.connection.close((err) => {
            if (err) reject(err);
            else resolve();
          });
        });
      }
    } catch (error) {
      console.error("Error closing connection:", error);
      throw error;
    } finally {
      this.connection = null;
    }
  }

  async getSchema() {
    if (!this.connection) {
      throw new Error("No active database connection");
    }

    try {
      switch (this.type) {
        case "postgresql":
          return this.getPostgreSQLSchema();
        case "mysql":
          return this.getMySQLSchema();
        case "sqlite":
          return this.getSQLiteSchema();
        default:
          throw new Error(`Unsupported database type: ${this.type}`);
      }
    } catch (error) {
      console.error("Error fetching schema:", error);
      throw new Error(`Failed to fetch schema: ${error.message}`);
    }
  }

  async getPostgreSQLSchema() {
    const tablesQuery = `
      SELECT 
        table_schema as schema,
        table_name as name,
        'table' as type
      FROM information_schema.tables
      WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
      ORDER BY table_schema, table_name;
    `;

    const { rows: tables } = await this.query(tablesQuery);

    // Get columns for each table
    for (const table of tables) {
      const columnsQuery = `
        SELECT 
          column_name as name,
          data_type as type,
          is_nullable as isNullable,
          column_default as defaultValue
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = $2
        ORDER BY ordinal_position;
      `;

      const { rows: columns } = await this.query(columnsQuery, [
        table.schema,
        table.name,
      ]);
      table.columns = columns;
    }

    return { tables };
  }

  async getMySQLSchema() {
    const tablesQuery = `
      SELECT 
        table_schema as schema,
        table_name as name,
        'table' as type
      FROM information_schema.tables
      WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
      ORDER BY table_schema, table_name;
    `;

    const { rows: tables } = await this.query(tablesQuery);

    // Get columns for each table
    for (const table of tables) {
      const columnsQuery = `
        SELECT 
          column_name as name,
          data_type as type,
          is_nullable as isNullable,
          column_default as defaultValue
        FROM information_schema.columns
        WHERE table_schema = ? AND table_name = ?
        ORDER BY ordinal_position;
      `;

      const { rows: columns } = await this.query(columnsQuery, [
        table.schema,
        table.name,
      ]);
      table.columns = columns;
    }

    return { tables };
  }

  async getSQLiteSchema() {
    // Get all tables
    const { rows: tables } = await this.query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );

    // Get schema for each table
    for (const table of tables) {
      const { rows: columns } = await this.query(
        `PRAGMA table_info(${table.name})`
      );
      table.columns = columns.map((col) => ({
        name: col.name,
        type: col.type,
        isNullable: col.notnull === 0,
        defaultValue: col.dflt_value,
      }));
      table.type = "table";
    }

    return { tables };
  }
}

// Connection manager to handle multiple connections
class ConnectionManager {
  constructor() {
    this.connections = new Map();
  }

  async createConnection(connectionConfig) {
    const { id, type, ...config } = connectionConfig;
    const connection = new DatabaseConnection(type, config);
    await connection.connect();
    this.connections.set(id, connection);
    return id;
  }

  getConnection(id) {
    const connection = this.connections.get(id);
    if (!connection) {
      throw new Error(`No connection found with id: ${id}`);
    }
    return connection;
  }

  async closeConnection(id) {
    const connection = this.connections.get(id);
    if (connection) {
      await connection.close();
      this.connections.delete(id);
    }
  }

  async closeAllConnections() {
    const closePromises = [];
    for (const [id] of this.connections) {
      closePromises.push(this.closeConnection(id));
    }
    await Promise.all(closePromises);
  }
}

// Create a singleton instance
const connectionManager = new ConnectionManager();

// Handle process termination
process.on("SIGINT", async () => {
  console.log("Closing all database connections...");
  await connectionManager.closeAllConnections();
  process.exit(0);
});

module.exports = {
  DatabaseConnection,
  connectionManager,
  connectToDatabase: async (config) => {
    const id = `conn-${Date.now()}`;
    await connectionManager.createConnection({ id, ...config });
    return id;
  },
};
