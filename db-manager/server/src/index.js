require("dotenv").config();
const http = require("http");
const app = require("./app");
const { connectionManager } = require("./db/connection");

// Get port from environment and store in Express.
const port = parseInt(process.env.PORT, 10) || 5001;
app.set("port", port);

// Create HTTP server.
const server = http.createServer(app);

// Event listener for HTTP server "error" event.
function onError(error) {
  if (error.syscall !== "listen") {
    throw error;
  }

  const bind = typeof port === "string" ? `Pipe ${port}` : `Port ${port}`;

  // Handle specific listen errors with friendly messages
  switch (error.code) {
    case "EACCES":
      console.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;
    case "EADDRINUSE":
      console.error(`${bind} is already in use`);
      process.exit(1);
      break;
    default:
      throw error;
  }
}

// Event listener for HTTP server "listening" event.
function onListening() {
  const addr = server.address();
  const bind = typeof addr === "string" ? `pipe ${addr}` : `port ${addr.port}`;
  console.log(`Server running in ${process.env.NODE_ENV} mode on ${bind}`);
}

// Listen on provided port, on all network interfaces.
server.listen(port);
server.on("error", onError);
server.on("listening", onListening);

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("Unhandled Rejection:", err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle graceful shutdown
process.on("SIGTERM", () => {
  console.info("SIGTERM received. Shutting down gracefully");
  connectionManager.closeAllConnections().then(() => {
    server.close(() => {
      console.log("Process terminated");
    });
  });
});

// Handle Ctrl+C
process.on("SIGINT", () => {
  console.info("SIGINT received. Shutting down gracefully");
  connectionManager.closeAllConnections().then(() => {
    server.close(() => {
      console.log("Process terminated");
      process.exit(0);
    });
  });
});

module.exports = server;
