import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Chip,
  Divider,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Alert,
  LinearProgress,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  PlayArrow as RunIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Download as ExportIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Clear as ClearIcon,
  FormatAlignLeft as FormatIcon,
  Share as ShareIcon,
  Bookmark as BookmarkIcon,
} from '@mui/icons-material';
import CodeMirror from '@uiw/react-codemirror';
import { sql } from '@codemirror/lang-sql';
import { oneDark } from '@codemirror/theme-one-dark';
import { EditorView } from '@codemirror/view';
import { useTheme } from '@mui/material/styles';
import { useConnection } from '../../contexts/ConnectionContext';
import QueryResults from './QueryResults';
import QueryHistory from './QueryHistory';
import { saveAs } from 'file-saver';

const AdvancedQueryEditor = () => {
  const theme = useTheme();
  const { activeConnection, executeQuery } = useConnection();
  const [query, setQuery] = useState('-- Welcome to Advanced Query Editor\n-- Start typing your SQL query here\n\nSELECT * FROM information_schema.tables LIMIT 10;');
  const [results, setResults] = useState(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionTime, setExecutionTime] = useState(null);
  const [error, setError] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [queryHistory, setQueryHistory] = useState([]);
  const [savedQueries, setSavedQueries] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [queryName, setQueryName] = useState('');
  const [queryDescription, setQueryDescription] = useState('');
  const [exportFormat, setExportFormat] = useState('csv');
  const [settingsAnchor, setSettingsAnchor] = useState(null);
  
  const editorRef = useRef();
  const containerRef = useRef();

  // Editor configuration
  const editorExtensions = [
    sql(),
    EditorView.theme({
      '&': {
        fontSize: '14px',
      },
      '.cm-content': {
        padding: '16px',
        minHeight: '200px',
      },
      '.cm-focused': {
        outline: 'none',
      },
      '.cm-editor': {
        borderRadius: '8px',
      },
      '.cm-scroller': {
        fontFamily: '"Fira Code", "Monaco", "Menlo", monospace',
      },
    }),
    EditorView.lineWrapping,
  ];

  if (theme.palette.mode === 'dark') {
    editorExtensions.push(oneDark);
  }

  // Execute query
  const handleExecuteQuery = useCallback(async () => {
    if (!activeConnection) {
      setError('No active database connection');
      return;
    }

    if (!query.trim()) {
      setError('Please enter a query');
      return;
    }

    setIsExecuting(true);
    setError(null);
    setResults(null);
    
    const startTime = Date.now();
    
    try {
      const result = await executeQuery(query.trim());
      const endTime = Date.now();
      const execTime = endTime - startTime;
      
      setResults(result);
      setExecutionTime(execTime);
      
      // Add to history
      const historyEntry = {
        id: Date.now(),
        query: query.trim(),
        timestamp: new Date(),
        executionTime: execTime,
        rowCount: result.rows?.length || 0,
        database: activeConnection.database,
      };
      setQueryHistory(prev => [historyEntry, ...prev.slice(0, 49)]); // Keep last 50 queries
      
    } catch (err) {
      setError(err.message);
    } finally {
      setIsExecuting(false);
    }
  }, [query, activeConnection, executeQuery]);

  // Save query
  const handleSaveQuery = () => {
    if (!queryName.trim()) return;
    
    const savedQuery = {
      id: Date.now(),
      name: queryName,
      description: queryDescription,
      query: query.trim(),
      createdAt: new Date(),
      database: activeConnection?.database,
    };
    
    setSavedQueries(prev => [savedQuery, ...prev]);
    setShowSaveDialog(false);
    setQueryName('');
    setQueryDescription('');
  };

  // Export results
  const handleExportResults = () => {
    if (!results || !results.rows) return;
    
    let content = '';
    let filename = `query_results_${new Date().toISOString().split('T')[0]}`;
    
    if (exportFormat === 'csv') {
      const headers = results.fields.map(field => field.name).join(',');
      const rows = results.rows.map(row => 
        results.fields.map(field => 
          JSON.stringify(row[field.name] || '')
        ).join(',')
      ).join('\n');
      content = headers + '\n' + rows;
      filename += '.csv';
    } else if (exportFormat === 'json') {
      content = JSON.stringify(results.rows, null, 2);
      filename += '.json';
    }
    
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    saveAs(blob, filename);
  };

  // Format query
  const handleFormatQuery = () => {
    // Basic SQL formatting - in a real app, you'd use a proper SQL formatter
    const formatted = query
      .replace(/\s+/g, ' ')
      .replace(/,/g, ',\n  ')
      .replace(/FROM/gi, '\nFROM')
      .replace(/WHERE/gi, '\nWHERE')
      .replace(/ORDER BY/gi, '\nORDER BY')
      .replace(/GROUP BY/gi, '\nGROUP BY')
      .replace(/HAVING/gi, '\nHAVING')
      .replace(/JOIN/gi, '\nJOIN')
      .replace(/LEFT JOIN/gi, '\nLEFT JOIN')
      .replace(/RIGHT JOIN/gi, '\nRIGHT JOIN')
      .replace(/INNER JOIN/gi, '\nINNER JOIN');
    
    setQuery(formatted);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleExecuteQuery();
      }
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        setShowSaveDialog(true);
      }
      if (e.key === 'F11') {
        e.preventDefault();
        setIsFullscreen(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleExecuteQuery]);

  return (
    <Box
      ref={containerRef}
      sx={{
        height: isFullscreen ? '100vh' : 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        bgcolor: 'background.default',
      }}
    >
      {/* Toolbar */}
      <Paper
        elevation={1}
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          borderRadius: isFullscreen ? 0 : 1,
        }}
      >
        <Button
          variant="contained"
          startIcon={<RunIcon />}
          onClick={handleExecuteQuery}
          disabled={isExecuting || !activeConnection}
          sx={{ minWidth: 120 }}
        >
          {isExecuting ? 'Running...' : 'Run Query'}
        </Button>
        
        <Divider orientation="vertical" flexItem />
        
        <Tooltip title="Save Query (Ctrl+S)">
          <IconButton onClick={() => setShowSaveDialog(true)} disabled={!query.trim()}>
            <SaveIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Query History">
          <IconButton onClick={() => setShowHistory(true)}>
            <HistoryIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Format Query">
          <IconButton onClick={handleFormatQuery}>
            <FormatIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Clear Editor">
          <IconButton onClick={() => setQuery('')}>
            <ClearIcon />
          </IconButton>
        </Tooltip>
        
        <Divider orientation="vertical" flexItem />
        
        <Tooltip title="Export Results">
          <IconButton 
            onClick={handleExportResults} 
            disabled={!results || !results.rows}
          >
            <ExportIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Settings">
          <IconButton onClick={(e) => setSettingsAnchor(e.currentTarget)}>
            <SettingsIcon />
          </IconButton>
        </Tooltip>
        
        <Box sx={{ flexGrow: 1 }} />
        
        {activeConnection && (
          <Chip
            label={`${activeConnection.type}: ${activeConnection.database || activeConnection.host}`}
            color="primary"
            size="small"
          />
        )}
        
        <Tooltip title={isFullscreen ? 'Exit Fullscreen (F11)' : 'Fullscreen (F11)'}>
          <IconButton onClick={() => setIsFullscreen(!isFullscreen)}>
            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>
        </Tooltip>
      </Paper>

      {/* Editor */}
      <Paper
        elevation={1}
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          mt: 1,
          overflow: 'hidden',
          borderRadius: isFullscreen ? 0 : 1,
        }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom>
            SQL Query Editor
          </Typography>
          {executionTime && (
            <Typography variant="caption" color="text.secondary">
              Last execution: {executionTime}ms
            </Typography>
          )}
        </Box>
        
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <CodeMirror
            ref={editorRef}
            value={query}
            onChange={setQuery}
            extensions={editorExtensions}
            style={{
              height: '100%',
              fontSize: '14px',
            }}
            placeholder="Enter your SQL query here..."
          />
        </Box>
      </Paper>

      {/* Progress bar */}
      {isExecuting && (
        <LinearProgress sx={{ mt: 1 }} />
      )}

      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ mt: 1 }}>
          {error}
        </Alert>
      )}

      {/* Results */}
      {results && (
        <Box sx={{ mt: 1, flex: 1, overflow: 'hidden' }}>
          <QueryResults results={results} />
        </Box>
      )}

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={() => setSettingsAnchor(null)}
      >
        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Export Format</InputLabel>
            <Select
              value={exportFormat}
              onChange={(e) => setExportFormat(e.target.value)}
              label="Export Format"
            >
              <MenuItem value="csv">CSV</MenuItem>
              <MenuItem value="json">JSON</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>
      </Menu>

      {/* Save Query Dialog */}
      <Dialog open={showSaveDialog} onClose={() => setShowSaveDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Save Query</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Query Name"
            fullWidth
            variant="outlined"
            value={queryName}
            onChange={(e) => setQueryName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (optional)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={queryDescription}
            onChange={(e) => setQueryDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSaveDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveQuery} variant="contained" disabled={!queryName.trim()}>
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Query History Dialog */}
      <QueryHistory
        open={showHistory}
        onClose={() => setShowHistory(false)}
        history={queryHistory}
        savedQueries={savedQueries}
        onSelectQuery={setQuery}
      />
    </Box>
  );
};

export default AdvancedQueryEditor;
