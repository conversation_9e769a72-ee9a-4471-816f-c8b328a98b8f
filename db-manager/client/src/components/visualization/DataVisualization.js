import React, { useState, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Bar<PERSON>hart as BarChartIcon,
  <PERSON><PERSON>hart as LineChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  ScatterPlot as ScatterPlotIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  Area,
  AreaChart,
} from 'recharts';
import { saveAs } from 'file-saver';

const CHART_TYPES = {
  bar: { name: 'Bar Chart', icon: BarChartIcon, component: <PERSON><PERSON><PERSON> },
  line: { name: 'Line Chart', icon: LineChartIcon, component: Line<PERSON>hart },
  area: { name: 'Area Chart', icon: LineChartIcon, component: AreaChart },
  pie: { name: 'Pie Chart', icon: PieChartIcon, component: PieChart },
  scatter: { name: 'Scatter Plot', icon: ScatterPlotIcon, component: ScatterChart },
};

const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
  '#ff00ff', '#00ffff', '#ff0000', '#0000ff', '#ffff00'
];

const DataVisualization = ({ data, onSaveChart }) => {
  const [chartType, setChartType] = useState('bar');
  const [xAxis, setXAxis] = useState('');
  const [yAxis, setYAxis] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [chartTitle, setChartTitle] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // Extract column information from data
  const columns = useMemo(() => {
    if (!data || !data.fields) return [];
    return data.fields.map(field => ({
      name: field.name,
      type: getColumnType(field.dataTypeID),
    }));
  }, [data]);

  // Get numeric and categorical columns
  const numericColumns = columns.filter(col => 
    ['number', 'int', 'float', 'decimal'].includes(col.type)
  );
  
  const categoricalColumns = columns.filter(col => 
    ['string', 'text', 'varchar'].includes(col.type) || 
    !['number', 'int', 'float', 'decimal', 'date', 'datetime'].includes(col.type)
  );

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!data || !data.rows || !xAxis || yAxis.length === 0) return [];
    
    return data.rows.slice(0, 100).map((row, index) => {
      const item = { 
        id: index,
        [xAxis]: row[xAxis] || `Row ${index + 1}` 
      };
      
      yAxis.forEach(col => {
        item[col] = parseFloat(row[col]) || 0;
      });
      
      return item;
    });
  }, [data, xAxis, yAxis]);

  // Auto-select initial axes
  React.useEffect(() => {
    if (columns.length > 0 && !xAxis) {
      // Auto-select first categorical column for X-axis
      const firstCategorical = categoricalColumns[0];
      if (firstCategorical) {
        setXAxis(firstCategorical.name);
      }
      
      // Auto-select first numeric column for Y-axis
      const firstNumeric = numericColumns[0];
      if (firstNumeric) {
        setYAxis([firstNumeric.name]);
      }
    }
  }, [columns, categoricalColumns, numericColumns, xAxis]);

  const getColumnType = (dataTypeID) => {
    const typeMap = {
      20: 'number', 21: 'number', 23: 'number',
      700: 'number', 701: 'number', 1700: 'number',
      16: 'boolean', 1114: 'datetime', 1082: 'date', 1083: 'time',
    };
    return typeMap[dataTypeID] || 'string';
  };

  const handleYAxisChange = (event) => {
    const value = event.target.value;
    setYAxis(typeof value === 'string' ? value.split(',') : value);
  };

  const exportChart = (format) => {
    const chartElement = document.querySelector('.recharts-wrapper svg');
    if (!chartElement) return;

    if (format === 'svg') {
      const svgData = new XMLSerializer().serializeToString(chartElement);
      const blob = new Blob([svgData], { type: 'image/svg+xml' });
      saveAs(blob, `chart_${Date.now()}.svg`);
    } else if (format === 'png') {
      // Convert SVG to PNG (requires canvas)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        canvas.toBlob(blob => {
          saveAs(blob, `chart_${Date.now()}.png`);
        });
      };
      
      const svgData = new XMLSerializer().serializeToString(chartElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;
    }
  };

  const saveChart = () => {
    if (onSaveChart) {
      onSaveChart({
        title: chartTitle,
        type: chartType,
        xAxis,
        yAxis,
        data: chartData,
        createdAt: new Date(),
      });
    }
    setShowSaveDialog(false);
    setChartTitle('');
  };

  const renderChart = () => {
    if (!chartData || chartData.length === 0) {
      return (
        <Alert severity="info" sx={{ m: 2 }}>
          No data available for visualization. Please select X and Y axes.
        </Alert>
      );
    }

    const commonProps = {
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 },
    };

    switch (chartType) {
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxis} />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            {yAxis.map((col, index) => (
              <Bar key={col} dataKey={col} fill={COLORS[index % COLORS.length]} />
            ))}
          </BarChart>
        );

      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxis} />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            {yAxis.map((col, index) => (
              <Line 
                key={col} 
                type="monotone" 
                dataKey={col} 
                stroke={COLORS[index % COLORS.length]} 
                strokeWidth={2}
              />
            ))}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxis} />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            {yAxis.map((col, index) => (
              <Area 
                key={col} 
                type="monotone" 
                dataKey={col} 
                stackId="1"
                stroke={COLORS[index % COLORS.length]} 
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </AreaChart>
        );

      case 'pie':
        const pieData = chartData.map(item => ({
          name: item[xAxis],
          value: item[yAxis[0]] || 0,
        }));
        
        return (
          <PieChart {...commonProps}>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <RechartsTooltip />
          </PieChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid />
            <XAxis dataKey={xAxis} />
            <YAxis />
            <RechartsTooltip cursor={{ strokeDasharray: '3 3' }} />
            <Legend />
            {yAxis.map((col, index) => (
              <Scatter 
                key={col} 
                name={col} 
                data={chartData} 
                fill={COLORS[index % COLORS.length]} 
              />
            ))}
          </ScatterChart>
        );

      default:
        return null;
    }
  };

  if (!data || !data.rows) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No data available for visualization. Execute a query first.
        </Typography>
      </Paper>
    );
  }

  return (
    <Box
      sx={{
        height: isFullscreen ? '100vh' : '100%',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        bgcolor: 'background.default',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Controls */}
      <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Chart Type</InputLabel>
              <Select
                value={chartType}
                onChange={(e) => setChartType(e.target.value)}
                label="Chart Type"
              >
                {Object.entries(CHART_TYPES).map(([key, type]) => (
                  <MenuItem key={key} value={key}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <type.icon fontSize="small" />
                      {type.name}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>X-Axis</InputLabel>
              <Select
                value={xAxis}
                onChange={(e) => setXAxis(e.target.value)}
                label="X-Axis"
              >
                {columns.map(col => (
                  <MenuItem key={col.name} value={col.name}>
                    {col.name} ({col.type})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Y-Axis</InputLabel>
              <Select
                multiple
                value={yAxis}
                onChange={handleYAxisChange}
                label="Y-Axis"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {numericColumns.map(col => (
                  <MenuItem key={col.name} value={col.name}>
                    {col.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Export as SVG">
                <IconButton onClick={() => exportChart('svg')} size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Save Chart">
                <IconButton onClick={() => setShowSaveDialog(true)} size="small">
                  <SaveIcon />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Fullscreen">
                <IconButton onClick={() => setIsFullscreen(!isFullscreen)} size="small">
                  <FullscreenIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Chart */}
      <Paper elevation={1} sx={{ flex: 1, p: 2, overflow: 'hidden' }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </Paper>

      {/* Save Chart Dialog */}
      <Dialog open={showSaveDialog} onClose={() => setShowSaveDialog(false)}>
        <DialogTitle>Save Chart</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Chart Title"
            fullWidth
            variant="outlined"
            value={chartTitle}
            onChange={(e) => setChartTitle(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSaveDialog(false)}>Cancel</Button>
          <Button onClick={saveChart} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DataVisualization;
