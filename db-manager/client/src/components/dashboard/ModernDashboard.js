import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Storage as DatabaseIcon,
  QueryStats as QueryIcon,
  Timeline as ChartIcon,
  Speed as PerformanceIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccessTime as TimeIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material';
import { useConnection } from '../../contexts/ConnectionContext';
import { formatDistanceToNow } from 'date-fns';

const ModernDashboard = () => {
  const { activeConnection, executeQuery, connectionStats } = useConnection();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [recentQueries, setRecentQueries] = useState([]);
  const [systemStats, setSystemStats] = useState(null);

  useEffect(() => {
    if (activeConnection) {
      loadDashboardData();
      const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [activeConnection]);

  const loadDashboardData = async () => {
    if (!activeConnection) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Load database statistics
      const stats = await getDatabaseStats();
      setDashboardData(stats);
      
      // Load recent queries from localStorage
      const queries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
      setRecentQueries(queries.slice(0, 5));
      
      // Load system performance stats
      const sysStats = await getSystemStats();
      setSystemStats(sysStats);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getDatabaseStats = async () => {
    let queries = {};
    
    switch (activeConnection.type) {
      case 'postgresql':
        queries = {
          tableCount: "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'pg_catalog')",
          viewCount: "SELECT COUNT(*) as count FROM information_schema.views WHERE table_schema NOT IN ('information_schema', 'pg_catalog')",
          databaseSize: "SELECT pg_size_pretty(pg_database_size(current_database())) as size",
          activeConnections: "SELECT COUNT(*) as count FROM pg_stat_activity WHERE state = 'active'",
        };
        break;
        
      case 'mysql':
        queries = {
          tableCount: "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()",
          viewCount: "SELECT COUNT(*) as count FROM information_schema.views WHERE table_schema = DATABASE()",
          databaseSize: "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as size FROM information_schema.tables WHERE table_schema = DATABASE()",
          activeConnections: "SELECT COUNT(*) as count FROM information_schema.processlist WHERE command != 'Sleep'",
        };
        break;
        
      case 'sqlite':
        queries = {
          tableCount: "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
          viewCount: "SELECT COUNT(*) as count FROM sqlite_master WHERE type='view'",
          databaseSize: "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()",
          activeConnections: "SELECT 1 as count", // SQLite doesn't have multiple connections
        };
        break;
    }
    
    const results = {};
    for (const [key, query] of Object.entries(queries)) {
      try {
        const result = await executeQuery(query);
        results[key] = result.rows[0];
      } catch (err) {
        console.error(`Error executing ${key} query:`, err);
        results[key] = { count: 0, size: 0 };
      }
    }
    
    return results;
  };

  const getSystemStats = async () => {
    // Simulate system performance metrics
    // In a real application, these would come from your backend monitoring
    return {
      cpuUsage: Math.random() * 100,
      memoryUsage: Math.random() * 100,
      diskUsage: Math.random() * 100,
      networkLatency: Math.random() * 50 + 10,
      uptime: '2 days, 14 hours',
      lastBackup: new Date(Date.now() - Math.random() * 86400000 * 7), // Random within last week
    };
  };

  const StatCard = ({ title, value, icon, color = 'primary', trend, subtitle }) => (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {trend > 0 ? (
              <TrendingUpIcon color="success" fontSize="small" />
            ) : (
              <TrendingDownIcon color="error" fontSize="small" />
            )}
            <Typography variant="body2" color={trend > 0 ? 'success.main' : 'error.main'}>
              {Math.abs(trend)}% from last week
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const PerformanceCard = ({ title, value, max = 100, color = 'primary' }) => (
    <Card elevation={2}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="h4" color={color}>
            {typeof value === 'number' ? `${value.toFixed(1)}%` : value}
          </Typography>
        </Box>
        {typeof value === 'number' && (
          <LinearProgress
            variant="determinate"
            value={value}
            color={value > 80 ? 'error' : value > 60 ? 'warning' : 'success'}
            sx={{ height: 8, borderRadius: 4 }}
          />
        )}
      </CardContent>
    </Card>
  );

  if (!activeConnection) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          Please connect to a database to view the dashboard.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Database Dashboard
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Chip
            icon={<DatabaseIcon />}
            label={`${activeConnection.type}: ${activeConnection.database || activeConnection.host}`}
            color="primary"
          />
          <Tooltip title="Refresh Dashboard">
            <IconButton onClick={loadDashboardData} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {loading && <LinearProgress sx={{ mb: 2 }} />}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Database Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Tables"
            value={dashboardData?.tableCount?.count || 0}
            icon={<DatabaseIcon />}
            color="primary"
            trend={5}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Views"
            value={dashboardData?.viewCount?.count || 0}
            icon={<QueryIcon />}
            color="secondary"
            trend={-2}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Database Size"
            value={dashboardData?.databaseSize?.size || '0 MB'}
            icon={<MemoryIcon />}
            color="info"
            subtitle="Total storage used"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Connections"
            value={dashboardData?.activeConnections?.count || 0}
            icon={<NetworkIcon />}
            color="warning"
            subtitle="Current sessions"
          />
        </Grid>

        {/* System Performance */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              System Performance
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <PerformanceCard
                  title="CPU Usage"
                  value={systemStats?.cpuUsage}
                  color="primary"
                />
              </Grid>
              <Grid item xs={6}>
                <PerformanceCard
                  title="Memory Usage"
                  value={systemStats?.memoryUsage}
                  color="secondary"
                />
              </Grid>
              <Grid item xs={6}>
                <PerformanceCard
                  title="Disk Usage"
                  value={systemStats?.diskUsage}
                  color="info"
                />
              </Grid>
              <Grid item xs={6}>
                <PerformanceCard
                  title="Network Latency"
                  value={`${systemStats?.networkLatency?.toFixed(1)}ms`}
                  color="warning"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Recent Queries */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Recent Queries
            </Typography>
            {recentQueries.length === 0 ? (
              <Alert severity="info">No recent queries found.</Alert>
            ) : (
              <List>
                {recentQueries.map((query, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        <QueryIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {query.query?.substring(0, 50)}...
                          </Typography>
                        }
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TimeIcon fontSize="small" />
                            <Typography variant="caption">
                              {formatDistanceToNow(new Date(query.timestamp), { addSuffix: true })}
                            </Typography>
                            {query.executionTime && (
                              <Chip
                                label={`${query.executionTime}ms`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < recentQueries.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<QueryIcon />}
                  onClick={() => window.location.hash = '#/query'}
                >
                  New Query
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<ChartIcon />}
                  onClick={() => window.location.hash = '#/visualize'}
                >
                  Create Chart
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<DatabaseIcon />}
                  onClick={() => window.location.hash = '#/schema'}
                >
                  Browse Schema
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<PerformanceIcon />}
                  onClick={() => window.location.hash = '#/monitoring'}
                >
                  Performance Monitor
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ModernDashboard;
